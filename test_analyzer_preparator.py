#!/usr/bin/env python3
"""
Test script to verify the BatteryTimelineAnalyzer works with DataPreparator.
"""

import tempfile
from pathlib import Path

from battery_timeline.analyzer import BatteryTimelineAnalyzer


def test_analyzer_with_preparator():
    """Test that analyzer can be instantiated and load data using DataPreparator."""
    print("Testing BatteryTimelineAnalyzer with DataPreparator...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        out_path = temp_path / "test_accure.csv"
        stats_path = temp_path / "test_stats.json"
        
        # Test that analyzer can be created without timeline_path
        analyzer = BatteryTimelineAnalyzer(
            out_path=out_path,
            stats_path=stats_path
        )
        
        # Verify initial state
        assert analyzer.prepared_data == {}
        assert analyzer.daily_stats_by_vehicle == {}
        assert analyzer.vin_to_vehicle_id == {}
        assert analyzer.timeline_df is None
        
        print("✅ Analyzer instantiation test passed!")


def test_analyzer_constructor_signature():
    """Test that the new constructor signature works correctly."""
    print("Testing analyzer constructor signature...")
    
    # Test with default paths
    analyzer1 = BatteryTimelineAnalyzer()
    assert analyzer1.out_path == Path("output/accure.csv")
    assert analyzer1.stats_path == Path("output/analyze_battery_timeline_statistics.txt")
    
    # Test with custom paths
    custom_out = Path("custom/output.csv")
    custom_stats = Path("custom/stats.json")
    analyzer2 = BatteryTimelineAnalyzer(
        out_path=custom_out,
        stats_path=custom_stats
    )
    assert analyzer2.out_path == custom_out
    assert analyzer2.stats_path == custom_stats
    
    print("✅ Constructor signature test passed!")


def test_analyzer_import():
    """Test that analyzer can be imported successfully."""
    print("Testing analyzer import...")
    
    from battery_timeline.analyzer import BatteryTimelineAnalyzer
    
    # Verify the class exists and has expected attributes
    assert hasattr(BatteryTimelineAnalyzer, '__init__')
    assert hasattr(BatteryTimelineAnalyzer, 'load')
    assert hasattr(BatteryTimelineAnalyzer, 'run')
    assert hasattr(BatteryTimelineAnalyzer, 'generate_output')
    
    print("✅ Import test passed!")


if __name__ == "__main__":
    test_analyzer_import()
    test_analyzer_constructor_signature()
    test_analyzer_with_preparator()
    print("\n🎉 All BatteryTimelineAnalyzer with DataPreparator tests passed!")
