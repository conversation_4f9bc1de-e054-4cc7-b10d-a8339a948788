from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Mapping, Sequence

import pandas as pd
import json
import logging

logger = logging.getLogger(__name__)


@dataclass
class OutputBundle:
    """Plain data ↔ OutputGenerator boundary."""

    records: Sequence[
        Mapping[str, Any]
    ]  # e.g. list[dict] or DataFrame.to_dict("records")
    stats: Mapping[str, Any]  # lightweight, JSON‑serialisable
    csv_path: Path
    stats_path: Path


class OutputGenerator:
    """No knowledge of batteries, timelines, or pandas groups."""

    def __init__(self):
        self.today = datetime.now().date()

    def __call__(self, bundle: OutputBundle) -> tuple[Path, Path]:
        """Generate output files from bundle data."""
        df = self._to_dataframe(bundle.records)
        self._write_csv(df, bundle.csv_path)
        self._write_stats(bundle.stats, bundle.stats_path)
        logger.info("✅ Output written: %s, %s", bundle.csv_path, bundle.stats_path)
        return bundle.csv_path, bundle.stats_path

    # ───────── helpers ─────────
    def _to_dataframe(self, records):
        """Convert records to DataFrame with basic sorting."""
        df = pd.DataFrame(records)
        if "battery_id" in df.columns:
            return df.sort_values("battery_id")
        return df

    def _write_csv(self, df: pd.DataFrame, path: Path):
        """Write DataFrame to CSV file."""
        path.parent.mkdir(parents=True, exist_ok=True)
        df.to_csv(path, index=False)
        logger.info(f"Saved data to {path}")

    def _write_stats(self, stats: Mapping[str, Any], path: Path):
        """Write stats to JSON file."""
        path.parent.mkdir(parents=True, exist_ok=True)
        with open(path, "w", encoding="utf‑8") as fh:
            json.dump(stats, fh, indent=2, default=str)
        logger.info(f"Saved statistics to {path}")
