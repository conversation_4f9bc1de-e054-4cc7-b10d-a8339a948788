from datetime import datetime
from typing import Dict, Any, List, Tuple
from collections import defaultdict
from battery_timeline.models import BatteryInterval

import pandas as pd
import logging

logger = logging.getLogger(__name__)


class OutputGenerator:
    """
    Handles generating CSV outputs, statistics files, and reports (including dual-battery report).
    """

    def __init__(self):
        self.today = datetime.now().date()

    def generate_outputs(
        self,
        timelines: List[BatteryInterval],
        stats: Dict[str, Any],
    ) -> Tuple[str, str]:
        """
        Generate output files.

        Args:
            timelines: List of BatteryInterval dicts
            stats: Dictionary containing statistics

        Returns:
            Tuple of (timeline_csv_path, stats_file_path)
        """
        logger.info("Generating output files...")

        # Generate enhanced timeline CSV output
        timeline_csv = None
        if timelines:
            timeline_df = pd.DataFrame(timelines)
            timeline_csv = f"output/battery_lifecycle_timelines.csv"
            timeline_df.to_csv(timeline_csv, index=False)
            logger.info(f"Saved timeline data to {timeline_csv}")

        # Generate dual-battery report
        dual_battery_vins = []
        if timelines:
            # Group intervals by VIN
            by_vin = defaultdict(list)
            for iv in timelines:
                by_vin[iv["vin"]].append(iv)

            for vin, intervals in by_vin.items():
                concurrent = len(
                    [iv for iv in intervals if iv["interval_end"] == self.today]
                )
                if concurrent == 2:
                    dual_battery_vins.append(vin)
                elif concurrent > 2:
                    logger.warning(f"VIN {vin} has {concurrent} active batteries")

        # Generate statistics file
        stats_filename = f"output/battery_timeline_statistics.txt"
        with open(stats_filename, "w") as f:
            f.write("Battery Timeline Analysis Statistics\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Processing Date: {datetime.now()}\n")
            f.write(f"Total Batteries: {stats.get('total_batteries', 0)}\n")
            f.write(f"Total Vehicles: {stats.get('total_vehicles', 0)}\n")
            f.write(f"Working-only Vehicles: {stats.get('working_only_vehicles', 0)}\n")
            f.write(f"Errors: {len(stats.get('errors', []))}\n")

            # Enhanced statistics
            if timelines:
                f.write(f"Total Timeline Intervals: {len(timelines)}\n")
                active_intervals = len(
                    [i for i in timelines if i["interval_end"] is None]
                )
                f.write(f"Currently Active Intervals: {active_intervals}\n")
                completed_intervals = len(timelines) - active_intervals
                f.write(f"Completed Intervals: {completed_intervals}\n")

            # Dual-battery configuration statistics
            f.write(f"\nDual-Battery Configuration:\n")
            f.write(f"  Vehicles with 2 active batteries: {len(dual_battery_vins)}\n")
            logger.info(
                f"  Vehicles with 2 active batteries: {len(dual_battery_vins)}\n"
            )
            if dual_battery_vins:
                f.write(
                    f"  VINs: {', '.join(dual_battery_vins[:10])}{'...' if len(dual_battery_vins) > 10 else ''}\n"
                )

            if stats.get("errors"):
                f.write("\nErrors:\n")
                for error in stats["errors"]:
                    f.write(f"- {error}\n")

        logger.info(f"Saved statistics to {stats_filename}")

        # Return the timeline CSV if it exists, otherwise return None
        return timeline_csv, stats_filename
