from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Tuple, Mapping, Sequence
from collections import defaultdict

import pandas as pd
import json
import logging

logger = logging.getLogger(__name__)


@dataclass
class OutputBundle:
    """Plain data ↔ OutputGenerator boundary."""

    records: Sequence[
        Mapping[str, Any]
    ]  # e.g. list[dict] or DataFrame.to_dict("records")
    stats: Mapping[str, Any]  # lightweight, JSON‑serialisable
    csv_path: Path
    stats_path: Path


class OutputGenerator:
    """No knowledge of batteries, timelines, or pandas groups."""

    def __init__(self):
        self.today = datetime.now().date()

    def __call__(self, bundle: OutputBundle) -> tuple[Path, Path]:
        """Generate output files from bundle data."""
        df = self._to_dataframe(bundle.records)
        self._write_csv(df, bundle.csv_path)
        self._write_stats(bundle.stats, bundle.stats_path)
        logger.info("✅ Output written: %s, %s", bundle.csv_path, bundle.stats_path)
        return bundle.csv_path, bundle.stats_path

    # ───────── helpers ─────────
    def _to_dataframe(self, records):
        """Convert records to DataFrame with basic sorting."""
        df = pd.DataFrame(records)
        if "battery_id" in df.columns:
            return df.sort_values("battery_id")
        return df

    def _write_csv(self, df: pd.DataFrame, path: Path):
        """Write DataFrame to CSV file."""
        path.parent.mkdir(parents=True, exist_ok=True)
        df.to_csv(path, index=False)
        logger.info(f"Saved data to {path}")

    def _write_stats(self, stats: Mapping[str, Any], path: Path):
        """Write stats to JSON file."""
        path.parent.mkdir(parents=True, exist_ok=True)
        with open(path, "w", encoding="utf‑8") as fh:
            json.dump(stats, fh, indent=2, default=str)
        logger.info(f"Saved statistics to {path}")

    # ───────── legacy compatibility ─────────
    def generate_outputs(
        self,
        timelines: List[Dict[str, Any]],
        stats: Dict[str, Any],
    ) -> Tuple[str, str]:
        """
        Legacy method for backward compatibility.

        Args:
            timelines: List of timeline dicts
            stats: Dictionary containing statistics

        Returns:
            Tuple of (timeline_csv_path, stats_file_path)
        """
        logger.info("Generating output files (legacy mode)...")

        # Generate enhanced timeline CSV output
        timeline_csv = None
        if timelines:
            timeline_df = pd.DataFrame(timelines)
            timeline_csv = "output/battery_lifecycle_timelines.csv"
            timeline_df.to_csv(timeline_csv, index=False)
            logger.info(f"Saved timeline data to {timeline_csv}")

        # Generate dual-battery report
        dual_battery_vins = []
        if timelines:
            # Group intervals by VIN
            by_vin = defaultdict(list)
            for iv in timelines:
                by_vin[iv["vin"]].append(iv)

            for vin, intervals in by_vin.items():
                concurrent = len(
                    [iv for iv in intervals if iv["interval_end"] == self.today]
                )
                if concurrent == 2:
                    dual_battery_vins.append(vin)
                elif concurrent > 2:
                    logger.warning(f"VIN {vin} has {concurrent} active batteries")

        # Generate statistics file
        stats_filename = "output/battery_timeline_statistics.txt"
        with open(stats_filename, "w") as f:
            f.write("Battery Timeline Analysis Statistics\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Processing Date: {datetime.now()}\n")
            f.write(f"Total Batteries: {stats.get('total_batteries', 0)}\n")
            f.write(f"Total Vehicles: {stats.get('total_vehicles', 0)}\n")
            f.write(f"Working-only Vehicles: {stats.get('working_only_vehicles', 0)}\n")
            f.write(f"Errors: {len(stats.get('errors', []))}\n")

            # Enhanced statistics
            if timelines:
                f.write(f"Total Timeline Intervals: {len(timelines)}\n")
                active_intervals = len(
                    [i for i in timelines if i["interval_end"] is None]
                )
                f.write(f"Currently Active Intervals: {active_intervals}\n")
                completed_intervals = len(timelines) - active_intervals
                f.write(f"Completed Intervals: {completed_intervals}\n")

            # Dual-battery configuration statistics
            f.write(f"\nDual-Battery Configuration:\n")
            f.write(f"  Vehicles with 2 active batteries: {len(dual_battery_vins)}\n")
            logger.info(
                f"  Vehicles with 2 active batteries: {len(dual_battery_vins)}\n"
            )
            if dual_battery_vins:
                f.write(
                    f"  VINs: {', '.join(dual_battery_vins[:10])}{'...' if len(dual_battery_vins) > 10 else ''}\n"
                )

            if stats.get("errors"):
                f.write("\nErrors:\n")
                for error in stats["errors"]:
                    f.write(f"- {error}\n")

        logger.info(f"Saved statistics to {stats_filename}")

        # Return the timeline CSV if it exists, otherwise return None
        return timeline_csv, stats_filename
