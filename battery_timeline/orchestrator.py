import logging
from pathlib import Path
from datetime import datetime

from battery_timeline.preparator import DataPreparator
from battery_timeline.processing import TimelineTransformer
from battery_timeline.validator import TimelineValidator
from battery_timeline.output_generator import OutputGenerator, OutputBundle

logger = logging.getLogger(__name__)


class BatteryTimelineGenerator:
    """
    Orchestrates the entire battery timeline analysis process.
    Coordinates DataPreparator, TimelineTransformer, TimelineValidator, and OutputGenerator.
    """

    def __init__(self):
        self.today = datetime.now().date()

        # Results from processing
        self.battery_timelines = []  # Final timeline results
        self.conflicts = []  # Conflicts found during processing

        self.stats = {
            "total_batteries": 0,
            "total_vehicles": 0,
            "working_only_vehicles": 0,
            "errors": [],
        }

    def run(self):
        """Run the battery timeline analysis process."""
        logger.info("Starting battery timeline analysis...")

        try:
            # Use DataPreparator to load and prepare all data
            logger.info("Preparing data using DataPreparator...")
            preparator = DataPreparator()
            prepared_data = preparator.load_and_prepare_data()

            # Update stats from prepared data
            self.stats = prepared_data["stats"]

            # Use TimelineTransformer to transform prepared data into timelines
            logger.info("Transforming data using TimelineTransformer...")
            transformer = TimelineTransformer(prepared_data)
            self.battery_timelines, self.conflicts = transformer.transform()

            # Validate timelines using TimelineValidator
            logger.info("Validating timelines using TimelineValidator...")
            validator = TimelineValidator()
            validation_result = validator.validate(self.battery_timelines)

            # Update stats with validation results
            self.stats.update(
                {
                    "validation_valid": validation_result["valid"],
                    "validation_errors": len(validation_result["errors"]),
                    "validation_successes": len(validation_result["successes"]),
                    "validation_violations": len(validation_result["violations"]),
                }
            )

            # Generate outputs using OutputGenerator
            logger.info("Generating outputs using OutputGenerator...")
            bundle = OutputBundle(
                records=self.battery_timelines,  # ← already list of dicts
                stats=self.stats,
                csv_path=Path("output/battery_lifecycle_timelines.csv"),
                stats_path=Path("output/battery_timeline_statistics.json"),
            )
            output_generator = OutputGenerator()
            csv_file, stats_file = output_generator(bundle)

            logger.info("Battery timeline analysis completed successfully!")
            if csv_file:
                logger.info(f"Timeline data saved to: {csv_file}")
            logger.info(f"Statistics saved to: {stats_file}")

            logger.info("Enhanced timeline analysis completed!")
            logger.info(f"Timeline intervals: {len(self.battery_timelines)}")
            logger.info(
                f"Total dual battery vehicles: {self.stats.get('total_dual_battery_vehicles', 0)}"
            )

            return csv_file, stats_file

        except Exception as e:
            logger.error(f"Error during processing: {e}")
            raise
